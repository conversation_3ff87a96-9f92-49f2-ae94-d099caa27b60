# Enhanced Error Handling & Loading States

This document describes the unified error handling and loading state patterns implemented in the Chikara frontend application.

## Overview

The enhanced error handling and loading state system provides:

- **Unified Error Handling**: Consistent error categorization, retry logic, and user-friendly messages
- **Flexible Loading States**: Multiple loading types with progress tracking and customizable displays
- **Async Operation Management**: Combined error handling and loading states for API calls
- **Reusable Components**: Pre-built components for common error and loading scenarios

## Core Hooks

### useErrorHandler

Provides unified error handling with automatic categorization and retry logic.

```typescript
import { useErrorHandler } from '@/hooks';

function MyComponent() {
  const errorHandler = useErrorHandler({
    maxRetries: 3,
    retryDelay: 1000,
    showToast: true,
    onError: (error) => console.log('Error:', error),
  });

  const handleApiCall = async () => {
    try {
      await someApiCall();
    } catch (error) {
      errorHandler.handleError(error);
    }
  };

  return (
    <div>
      {errorHandler.hasError && (
        <ErrorDisplay 
          error={errorHandler.error} 
          onRetry={errorHandler.retry}
          onDismiss={errorHandler.clearError}
        />
      )}
    </div>
  );
}
```

### useLoadingState

Manages loading states with different types and progress tracking.

```typescript
import { useLoadingState } from '@/hooks';

function MyComponent() {
  const loading = useLoadingState({
    type: 'spinner',
    minDuration: 300, // Prevent flashing
  });

  const handleAction = async () => {
    loading.startLoading({ message: 'Processing...' });
    try {
      await someAsyncOperation();
    } finally {
      loading.stopLoading();
    }
  };

  return (
    <div>
      {loading.isLoading && (
        <LoadingSpinner variant={loading.loadingState.type} />
      )}
    </div>
  );
}
```

### useAsyncOperation

Combines error handling and loading states for async operations.

```typescript
import { useAsyncOperation } from '@/hooks';

function MyComponent() {
  const operation = useAsyncOperation(
    async (id: number) => {
      return await api.getData(id);
    },
    {
      loadingMessage: 'Fetching data...',
      successMessage: 'Data loaded successfully!',
      retryable: true,
      maxRetries: 3,
    }
  );

  return (
    <div>
      <button 
        onClick={() => operation.execute(123)}
        disabled={operation.isLoading}
      >
        {operation.isLoading ? 'Loading...' : 'Load Data'}
      </button>

      {operation.hasError && (
        <ErrorDisplay 
          error={operation.error} 
          onRetry={operation.retry}
        />
      )}

      {operation.data && (
        <div>Data: {JSON.stringify(operation.data)}</div>
      )}
    </div>
  );
}
```

## Specialized Hooks

### useApiOperation

Pre-configured for API calls with sensible defaults.

```typescript
import { useApiOperation } from '@/hooks';

function MyComponent() {
  const apiOp = useApiOperation(
    async (params) => await api.someEndpoint(params),
    {
      loadingMessage: 'Calling API...',
      successMessage: 'Success!',
    }
  );

  return (
    <button onClick={() => apiOp.execute({ id: 1 })}>
      Call API
    </button>
  );
}
```

### useFormSubmission

Optimized for form submissions with validation error handling.

```typescript
import { useFormSubmission } from '@/hooks';

function MyForm() {
  const submission = useFormSubmission(
    async (formData) => await api.submitForm(formData),
    {
      loadingMessage: 'Submitting...',
      successMessage: 'Form submitted successfully!',
    }
  );

  const handleSubmit = (data) => {
    submission.execute(data);
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* form fields */}
      <button 
        type="submit" 
        disabled={submission.isLoading}
      >
        {submission.isLoading ? 'Submitting...' : 'Submit'}
      </button>
    </form>
  );
}
```

## Components

### Loading Components

```typescript
import { 
  LoadingSpinner, 
  Skeleton, 
  LoadingOverlay 
} from '@/components/Loading';

// Spinner with different variants
<LoadingSpinner variant="dots" size="lg" color="primary" />

// Skeleton loaders
<Skeleton variant="text" width="80%" height="1rem" />
<TextSkeleton lines={3} />
<CardSkeleton showAvatar showActions />

// Overlay loading
<LoadingOverlay 
  isVisible={isLoading}
  message="Processing..."
  progress={progress}
  variant="spinner"
/>
```

### Error Components

```typescript
import { 
  ErrorDisplay, 
  CardErrorDisplay, 
  BannerErrorDisplay 
} from '@/components/Error';

// Basic error display
<ErrorDisplay 
  error={error}
  onRetry={handleRetry}
  onDismiss={handleDismiss}
  showDetails
/>

// Specialized variants
<CardErrorDisplay error={error} onRetry={handleRetry} />
<BannerErrorDisplay error={error} onDismiss={handleDismiss} />
```

### Enhanced Error Boundary

```typescript
import EnhancedErrorBoundary from '@/components/Layout/EnhancedErrorBoundary';

function App() {
  return (
    <EnhancedErrorBoundary
      level="page"
      enableRetry
      showDetails
      onError={(error, errorInfo) => {
        // Log to error reporting service
        console.error('Boundary caught:', error);
      }}
    >
      <MyComponent />
    </EnhancedErrorBoundary>
  );
}
```

## Error Types

The system automatically categorizes errors:

- **network**: Connection issues, timeouts (retryable)
- **server**: 5xx HTTP errors (retryable)
- **validation**: 4xx HTTP errors (non-retryable)
- **auth**: Authentication/authorization errors (non-retryable)
- **maintenance**: Maintenance mode (non-retryable)
- **unknown**: Uncategorized errors

## Loading Types

Available loading types:

- **spinner**: Rotating spinner indicator
- **skeleton**: Placeholder content shapes
- **overlay**: Full-screen or component overlay
- **inline**: Inline loading indicator
- **button**: Button-specific loading state

## Best Practices

### 1. Use Appropriate Hooks

- `useApiOperation` for API calls
- `useFormSubmission` for form handling
- `useFileUpload` for file operations
- `useAsyncOperation` for custom async operations

### 2. Error Handling

```typescript
// Good: Let the hook handle errors
const operation = useApiOperation(apiCall, {
  onError: (error) => {
    // Custom error handling if needed
    if (error.type === 'validation') {
      setFormErrors(error.details);
    }
  }
});

// Avoid: Manual error handling
try {
  await apiCall();
} catch (error) {
  toast.error(error.message); // Hook does this automatically
}
```

### 3. Loading States

```typescript
// Good: Use appropriate loading type
const loading = useLoadingState({ 
  type: 'skeleton', // For content loading
  minDuration: 300  // Prevent flashing
});

// Good: Show progress for long operations
const upload = useFileUpload(uploadFile, {
  onProgress: (progress) => {
    // Progress is automatically tracked
  }
});
```

### 4. Component Integration

```typescript
// Good: Consistent error and loading display
function DataComponent() {
  const data = useApiOperation(fetchData);

  if (data.isLoading) {
    return <Skeleton variant="card" />;
  }

  if (data.hasError) {
    return <CardErrorDisplay error={data.error} onRetry={data.retry} />;
  }

  return <DataDisplay data={data.data} />;
}
```

## Migration Guide

### From Old Patterns

```typescript
// Old pattern
const [loading, setLoading] = useState(false);
const [error, setError] = useState(null);

const handleAction = async () => {
  setLoading(true);
  setError(null);
  try {
    await apiCall();
    toast.success('Success!');
  } catch (err) {
    setError(err);
    toast.error(err.message);
  } finally {
    setLoading(false);
  }
};

// New pattern
const action = useApiOperation(apiCall, {
  successMessage: 'Success!'
});

const handleAction = () => action.execute();
```

## Testing

The hooks and components are fully tested. See the test files for examples:

- `hooks/__tests__/useErrorHandler.test.ts`
- `hooks/__tests__/useLoadingState.test.ts`
- `components/__tests__/ErrorDisplay.test.tsx`

## Example Component

See `components/Examples/EnhancedErrorHandlingExample.tsx` for a comprehensive demonstration of all patterns.
