import { renderHook, act } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import useTrainStat from "../useTrainStat";
import type { ReactNode } from "react";

// Mock the API
vi.mock("@/helpers/api", () => ({
    api: {
        user: {
            train: {
                mutate: vi.fn(),
            },
            getCurrentUserInfo: {
                key: () => ["user", "current"],
            },
        },
    },
}));

// Mock the async operation hook
vi.mock("@/hooks/useAsyncOperation", () => ({
    useFormSubmission: vi.fn((asyncFn, options) => ({
        execute: vi.fn(),
        isLoading: false,
        hasError: false,
        error: null,
        data: null,
        reset: vi.fn(),
        retry: vi.fn(),
        canRetry: false,
        retryCount: 0,
        isRetrying: false,
        loadingState: {
            isLoading: false,
            type: "button",
            message: "Training in progress...",
        },
        updateProgress: vi.fn(),
        updateMessage: vi.fn(),
    })),
}));

// Mock react-hot-toast
vi.mock("react-hot-toast", () => ({
    toast: {
        success: vi.fn(),
        error: vi.fn(),
    },
}));

const createWrapper = () => {
    const queryClient = new QueryClient({
        defaultOptions: {
            queries: { retry: false },
            mutations: { retry: false },
        },
    });

    return ({ children }: { children: ReactNode }) => (
        <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    );
};

describe("useTrainStat", () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    it("should initialize with correct default state", () => {
        const { result } = renderHook(() => useTrainStat(), {
            wrapper: createWrapper(),
        });

        expect(result.current.isLoading).toBe(false);
        expect(result.current.hasError).toBe(false);
        expect(result.current.error).toBeNull();
        expect(result.current.data).toBeNull();
        expect(typeof result.current.mutate).toBe("function");
        expect(typeof result.current.retry).toBe("function");
        expect(typeof result.current.reset).toBe("function");
    });

    it("should provide enhanced async operation functionality", () => {
        const { result } = renderHook(() => useTrainStat(), {
            wrapper: createWrapper(),
        });

        // Check that all enhanced functionality is available
        expect(result.current).toHaveProperty("loadingState");
        expect(result.current).toHaveProperty("updateProgress");
        expect(result.current).toHaveProperty("updateMessage");
        expect(result.current).toHaveProperty("canRetry");
        expect(result.current).toHaveProperty("retryCount");
        expect(result.current).toHaveProperty("isRetrying");
    });

    it("should call success callback when provided", () => {
        const onSuccess = vi.fn();
        const onError = vi.fn();

        const { result } = renderHook(
            () => useTrainStat({ onSuccess, onError }),
            {
                wrapper: createWrapper(),
            }
        );

        expect(result.current).toBeDefined();
        expect(onSuccess).not.toHaveBeenCalled();
        expect(onError).not.toHaveBeenCalled();
    });

    it("should expose mutate function for compatibility", () => {
        const { result } = renderHook(() => useTrainStat(), {
            wrapper: createWrapper(),
        });

        expect(typeof result.current.mutate).toBe("function");
        expect(typeof result.current.mutateAsync).toBe("function");
    });

    it("should provide loading state information", () => {
        const { result } = renderHook(() => useTrainStat(), {
            wrapper: createWrapper(),
        });

        expect(result.current.loadingState).toEqual({
            isLoading: false,
            type: "button",
            message: "Training in progress...",
        });
    });

    it("should provide retry functionality", () => {
        const { result } = renderHook(() => useTrainStat(), {
            wrapper: createWrapper(),
        });

        expect(typeof result.current.retry).toBe("function");
        expect(result.current.canRetry).toBe(false);
        expect(result.current.retryCount).toBe(0);
        expect(result.current.isRetrying).toBe(false);
    });

    it("should provide reset functionality", () => {
        const { result } = renderHook(() => useTrainStat(), {
            wrapper: createWrapper(),
        });

        expect(typeof result.current.reset).toBe("function");
    });

    it("should provide progress update functionality", () => {
        const { result } = renderHook(() => useTrainStat(), {
            wrapper: createWrapper(),
        });

        expect(typeof result.current.updateProgress).toBe("function");
        expect(typeof result.current.updateMessage).toBe("function");
    });
});
