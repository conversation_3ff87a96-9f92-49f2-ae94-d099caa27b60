import { useQueryClient } from "@tanstack/react-query";
import { api } from "@/helpers/api";
import { useFormSubmission } from "@/hooks/useAsyncOperation";
import type { UserStat } from "@/types/user";

export interface TrainingResult {
    statProgress?: {
        leveledUp: boolean;
        currentLevel: number;
        levelsGained: number;
        expGained: number;
        statName: string;
    };
    focusRemaining?: number;
    dailyFatigueRemaining?: number;
}

interface UseTrainStatProps {
    onSuccess?: (_result: TrainingResult, _statName: string) => void;
    onError?: (_error: any) => void;
}

const useTrainStat = ({ onSuccess, onError }: UseTrainStatProps = {}) => {
    const queryClient = useQueryClient();

    const trainingOperation = useFormSubmission(
        async ({ stat, focusAmount }: { stat: UserStat; focusAmount: number }) => {
            const response = await api.user.train.mutate({ stat, focusAmount });
            return { response, stat };
        },
        {
            loadingMessage: "Training in progress...",
            successMessage: "Training completed successfully!",
            onSuccess: ({ response, stat }) => {
                const { statProgress, focusRemaining, dailyFatigueRemaining } = response;

                // Add statName to progress if it exists
                const result: TrainingResult = {
                    statProgress: statProgress
                        ? {
                              ...statProgress,
                              statName: stat,
                          }
                        : undefined,
                    focusRemaining,
                    dailyFatigueRemaining,
                };

                // Call the success callback
                onSuccess?.(result, stat);

                // Invalidate queries to refresh user data
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
            },
            onError: (error) => {
                console.error("Training error:", error);
                onError?.(error);
            },
            // Training-specific configuration
            retryable: true,
            maxRetries: 2,
            retryDelay: 1000,
        }
    );

    return {
        // Expose the execute function as mutate for compatibility
        mutate: trainingOperation.execute,
        mutateAsync: trainingOperation.execute,

        // Loading states
        isPending: trainingOperation.isLoading,
        isLoading: trainingOperation.isLoading,

        // Error states
        error: trainingOperation.error,
        isError: trainingOperation.hasError,

        // Success state
        isSuccess: !trainingOperation.isLoading && !trainingOperation.hasError && trainingOperation.data !== null,
        data: trainingOperation.data,

        // Additional functionality from enhanced async operation
        reset: trainingOperation.reset,
        retry: trainingOperation.retry,
        canRetry: trainingOperation.canRetry,
        retryCount: trainingOperation.retryCount,
        isRetrying: trainingOperation.isRetrying,

        // Loading state management
        loadingState: trainingOperation.loadingState,
        updateProgress: trainingOperation.updateProgress,
        updateMessage: trainingOperation.updateMessage,
    };
};

export default useTrainStat;
