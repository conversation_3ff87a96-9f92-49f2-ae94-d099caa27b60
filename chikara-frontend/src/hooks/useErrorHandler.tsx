import { useCallback, useState } from "react";
import { toast } from "react-hot-toast";
import { ApiError, UnauthorizedError, ForbiddenError, MaintenanceModeError } from "@/helpers/apiError";
import handleLogout from "@/helpers/handleLogout";

export interface AppError {
    message: string;
    code?: string;
    status?: number;
    type: "network" | "validation" | "server" | "auth" | "maintenance" | "unknown";
    retryable: boolean;
    originalError?: unknown;
}

export interface UseErrorHandlerOptions {
    onError?: (error: AppError) => void;
    fallbackMessage?: string;
    autoRetry?: boolean;
    maxRetries?: number;
    retryDelay?: number;
    showToast?: boolean;
    logError?: boolean;
}

export interface UseErrorHandlerReturn {
    error: AppError | null;
    hasError: boolean;
    retryCount: number;
    isRetrying: boolean;
    clearError: () => void;
    handleError: (error: unknown) => AppError;
    retry: () => Promise<void>;
    canRetry: boolean;
}

const DEFAULT_OPTIONS: Required<UseErrorHandlerOptions> = {
    onError: () => {},
    fallbackMessage: "An unexpected error occurred",
    autoRetry: false,
    maxRetries: 3,
    retryDelay: 1000,
    showToast: true,
    logError: true,
};

/**
 * Categorizes an error into a specific type for better handling
 */
function categorizeError(error: unknown): AppError["type"] {
    if (error instanceof UnauthorizedError || error instanceof ForbiddenError) {
        return "auth";
    }
    if (error instanceof MaintenanceModeError) {
        return "maintenance";
    }
    if (error instanceof ApiError) {
        if (error.status && error.status >= 400 && error.status < 500) {
            return "validation";
        }
        if (error.status && error.status >= 500) {
            return "server";
        }
        return "network";
    }
    if (error instanceof Error) {
        if (error.message.includes("network") || error.message.includes("fetch")) {
            return "network";
        }
    }
    return "unknown";
}

/**
 * Determines if an error is retryable based on its type and characteristics
 */
function isRetryable(error: unknown, type: AppError["type"]): boolean {
    // Never retry auth or maintenance errors
    if (type === "auth" || type === "maintenance") {
        return false;
    }
    
    // Retry network and server errors
    if (type === "network" || type === "server") {
        return true;
    }
    
    // Don't retry validation errors (4xx)
    if (type === "validation") {
        return false;
    }
    
    // For unknown errors, check if it's a network-related error
    if (type === "unknown" && error instanceof Error) {
        return error.message.includes("network") || error.message.includes("timeout");
    }
    
    return false;
}

/**
 * Converts any error into a standardized AppError format
 */
function normalizeError(error: unknown, fallbackMessage: string): AppError {
    const type = categorizeError(error);
    const retryable = isRetryable(error, type);
    
    if (error instanceof ApiError) {
        return {
            message: error.message,
            code: error.name,
            status: error.status,
            type,
            retryable,
            originalError: error,
        };
    }
    
    if (error instanceof Error) {
        return {
            message: error.message,
            code: error.name,
            type,
            retryable,
            originalError: error,
        };
    }
    
    return {
        message: fallbackMessage,
        type: "unknown",
        retryable: false,
        originalError: error,
    };
}

/**
 * Unified error handling hook that provides consistent error management
 * across the application with retry logic and user-friendly error messages
 */
export function useErrorHandler(options: UseErrorHandlerOptions = {}): UseErrorHandlerReturn {
    const config = { ...DEFAULT_OPTIONS, ...options };
    
    const [error, setError] = useState<AppError | null>(null);
    const [retryCount, setRetryCount] = useState(0);
    const [isRetrying, setIsRetrying] = useState(false);
    const [retryFunction, setRetryFunction] = useState<(() => Promise<void>) | null>(null);
    
    const clearError = useCallback(() => {
        setError(null);
        setRetryCount(0);
        setIsRetrying(false);
        setRetryFunction(null);
    }, []);
    
    const handleError = useCallback((rawError: unknown): AppError => {
        const normalizedError = normalizeError(rawError, config.fallbackMessage);
        
        // Log error if enabled
        if (config.logError) {
            console.error("Error handled by useErrorHandler:", {
                error: normalizedError,
                originalError: rawError,
            });
        }
        
        // Handle special error types
        if (normalizedError.type === "auth") {
            handleLogout();
            return normalizedError;
        }
        
        if (normalizedError.type === "maintenance") {
            if (config.showToast) {
                toast.error(normalizedError.message, { duration: Number.POSITIVE_INFINITY });
            }
            setError(normalizedError);
            config.onError(normalizedError);
            return normalizedError;
        }
        
        // Show toast notification if enabled
        if (config.showToast) {
            toast.error(normalizedError.message);
        }
        
        setError(normalizedError);
        config.onError(normalizedError);
        
        return normalizedError;
    }, [config]);
    
    const retry = useCallback(async () => {
        if (!retryFunction || !error?.retryable || retryCount >= config.maxRetries) {
            return;
        }
        
        setIsRetrying(true);
        
        try {
            // Add delay before retry
            if (config.retryDelay > 0) {
                await new Promise(resolve => setTimeout(resolve, config.retryDelay));
            }
            
            await retryFunction();
            clearError();
        } catch (retryError) {
            setRetryCount(prev => prev + 1);
            handleError(retryError);
        } finally {
            setIsRetrying(false);
        }
    }, [retryFunction, error, retryCount, config.maxRetries, config.retryDelay, clearError, handleError]);
    
    // Auto-retry logic
    const autoRetry = useCallback(async () => {
        if (config.autoRetry && error?.retryable && retryCount < config.maxRetries && !isRetrying) {
            await retry();
        }
    }, [config.autoRetry, error, retryCount, config.maxRetries, isRetrying, retry]);
    
    // Set up auto-retry when error changes
    useState(() => {
        if (error) {
            autoRetry();
        }
    });
    
    return {
        error,
        hasError: !!error,
        retryCount,
        isRetrying,
        clearError,
        handleError,
        retry,
        canRetry: !!error?.retryable && retryCount < config.maxRetries && !isRetrying,
    };
}

export default useErrorHandler;
