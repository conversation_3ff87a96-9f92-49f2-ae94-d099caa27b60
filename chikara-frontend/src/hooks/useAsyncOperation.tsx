import React, { useCallback, useRef } from "react";
import { useE<PERSON><PERSON><PERSON><PERSON><PERSON>, type UseErrorHandlerOptions, type AppError } from "./useErrorHandler";
import { useLoadingState, type UseLoadingStateOptions, type LoadingState } from "./useLoadingState";

export interface UseAsyncOperationOptions<T> extends UseErrorHandlerOptions, UseLoadingStateOptions {
    onSuccess?: (data: T) => void;
    onError?: (error: AppError) => void;
    onFinally?: () => void;
    retryable?: boolean;
    loadingMessage?: string;
    successMessage?: string;
    immediate?: boolean; // Execute immediately on mount
}

export interface UseAsyncOperationReturn<T> {
    data: T | null;
    execute: (...args: any[]) => Promise<T | null>;
    reset: () => void;

    // Loading state
    isLoading: boolean;
    loadingState: LoadingState;
    updateProgress: (progress: number) => void;
    updateMessage: (message: string) => void;

    // Error state
    error: AppError | null;
    hasError: boolean;
    clearError: () => void;
    retry: () => Promise<void>;
    canRetry: boolean;
    retryCount: number;
    isRetrying: boolean;
}

/**
 * Enhanced async operation hook that combines error handling and loading states
 * for a unified interface to handle async operations like API calls
 */
export function useAsyncOperation<T = any>(
    asyncFunction: (...args: any[]) => Promise<T>,
    options: UseAsyncOperationOptions<T> = {}
): UseAsyncOperationReturn<T> {
    const {
        onSuccess,
        onError,
        onFinally,
        retryable = true,
        loadingMessage = "Loading...",
        successMessage,
        immediate = false,
        ...restOptions
    } = options;

    const dataRef = useRef<T | null>(null);
    const lastArgsRef = useRef<any[]>([]);

    // Initialize error handler
    const errorHandler = useErrorHandler({
        ...restOptions,
        onError: (error) => {
            onError?.(error);
            restOptions.onError?.(error);
        },
    });

    // Initialize loading state
    const loadingStateManager = useLoadingState({
        ...restOptions,
        message: loadingMessage,
    });

    const execute = useCallback(
        async (...args: any[]): Promise<T | null> => {
            try {
                // Clear previous error
                errorHandler.clearError();

                // Start loading
                loadingStateManager.startLoading({
                    message: loadingMessage,
                });

                // Store args for retry functionality
                lastArgsRef.current = args;

                // Execute the async function
                const result = await asyncFunction(...args);

                // Store successful result
                dataRef.current = result;

                // Call success callback
                onSuccess?.(result);

                // Show success message if provided
                if (successMessage) {
                    const { toast } = await import("react-hot-toast");
                    toast.success(successMessage);
                }

                return result;
            } catch (error) {
                // Handle error
                const normalizedError = errorHandler.handleError(error);

                // Don't store data on error
                dataRef.current = null;

                return null;
            } finally {
                // Stop loading
                loadingStateManager.stopLoading();

                // Call finally callback
                onFinally?.();
            }
        },
        [asyncFunction, errorHandler, loadingStateManager, onSuccess, onFinally, loadingMessage, successMessage]
    );

    const retry = useCallback(async (): Promise<void> => {
        if (lastArgsRef.current.length > 0) {
            await execute(...lastArgsRef.current);
        } else {
            await execute();
        }
    }, [execute]);

    const reset = useCallback(() => {
        dataRef.current = null;
        lastArgsRef.current = [];
        errorHandler.clearError();
        loadingStateManager.stopLoading();
    }, [errorHandler, loadingStateManager]);

    // Execute immediately if requested (using useEffect to avoid issues)
    React.useEffect(() => {
        if (immediate && !loadingStateManager.isLoading && !errorHandler.hasError && !dataRef.current) {
            execute();
        }
    }, [immediate, execute, loadingStateManager.isLoading, errorHandler.hasError]);

    return {
        data: dataRef.current,
        execute,
        reset,

        // Loading state
        isLoading: loadingStateManager.isLoading,
        loadingState: loadingStateManager.loadingState,
        updateProgress: loadingStateManager.updateProgress,
        updateMessage: loadingStateManager.updateMessage,

        // Error state
        error: errorHandler.error,
        hasError: errorHandler.hasError,
        clearError: errorHandler.clearError,
        retry: retryable ? retry : async () => {},
        canRetry: retryable && errorHandler.canRetry,
        retryCount: errorHandler.retryCount,
        isRetrying: errorHandler.isRetrying,
    };
}

/**
 * Specialized hook for API operations with React Query integration
 */
export function useApiOperation<T = any>(
    apiFunction: (...args: any[]) => Promise<T>,
    options: UseAsyncOperationOptions<T> = {}
) {
    return useAsyncOperation(apiFunction, {
        type: "spinner",
        showToast: true,
        logError: true,
        maxRetries: 3,
        retryDelay: 1000,
        ...options,
    });
}

/**
 * Hook for form submissions with validation and error handling
 */
export function useFormSubmission<T = any>(
    submitFunction: (...args: any[]) => Promise<T>,
    options: UseAsyncOperationOptions<T> = {}
) {
    return useAsyncOperation(submitFunction, {
        type: "button",
        showToast: true,
        loadingMessage: "Submitting...",
        successMessage: "Submitted successfully!",
        maxRetries: 1, // Usually don't retry form submissions
        ...options,
    });
}

/**
 * Hook for file upload operations with progress tracking
 */
export function useFileUpload<T = any>(
    uploadFunction: (file: File, onProgress?: (progress: number) => void) => Promise<T>,
    options: UseAsyncOperationOptions<T> = {}
) {
    const asyncOp = useAsyncOperation((file: File) => uploadFunction(file, asyncOp.updateProgress), {
        type: "overlay",
        loadingMessage: "Uploading file...",
        successMessage: "File uploaded successfully!",
        ...options,
    });

    return asyncOp;
}

export default useAsyncOperation;
