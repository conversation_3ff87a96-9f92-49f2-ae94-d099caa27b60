import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useFormSubmission } from "@/hooks/useAsyncOperation";
import { toast } from "react-hot-toast";

/**
 * Enhanced version of useActivateItem using the new unified patterns
 * This demonstrates how to integrate the new error handling and loading state patterns
 */
export const useEnhancedActivateItem = (onSuccessCallback?: () => void) => {
    const queryClient = useQueryClient();

    // Using the new useFormSubmission hook for better UX
    const asyncOperation = useFormSubmission(
        async (itemId: number) => {
            const result = await api.user.useItem.mutate({ itemId });
            return result;
        },
        {
            loadingMessage: "Using item...",
            successMessage: "Item used successfully!",
            onSuccess: (data) => {
                // Invalidate relevant queries
                queryClient.invalidateQueries({ queryKey: api.user.getInventory.key() });
                queryClient.invalidateQueries({ queryKey: api.user.getCurrentUserInfo.key() });
                queryClient.invalidateQueries({ queryKey: api.user.getStatusEffects.key() });
                
                // Call custom success callback
                onSuccessCallback?.();
            },
            onError: (error) => {
                // Error is automatically handled by the useFormSubmission hook
                console.error("Failed to use item:", error);
            },
            // Configure retry behavior
            retryable: true,
            maxRetries: 2,
            retryDelay: 1000,
        }
    );

    return {
        // Expose the execute function as mutate for compatibility
        mutate: asyncOperation.execute,
        mutateAsync: asyncOperation.execute,
        
        // Loading states
        isPending: asyncOperation.isLoading,
        isLoading: asyncOperation.isLoading,
        
        // Error states
        error: asyncOperation.error,
        isError: asyncOperation.hasError,
        
        // Success state
        isSuccess: !asyncOperation.isLoading && !asyncOperation.hasError && asyncOperation.data !== null,
        data: asyncOperation.data,
        
        // Additional functionality
        reset: asyncOperation.reset,
        retry: asyncOperation.retry,
        canRetry: asyncOperation.canRetry,
    };
};

/**
 * Traditional React Query mutation for comparison/fallback
 */
export const useActivateItemMutation = (onSuccessCallback?: () => void) => {
    const queryClient = useQueryClient();

    return useMutation(
        api.user.useItem.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: api.user.getInventory.key() });
                queryClient.invalidateQueries({ queryKey: api.user.getCurrentUserInfo.key() });
                queryClient.invalidateQueries({ queryKey: api.user.getStatusEffects.key() });
                toast.success("Item used successfully!");

                if (onSuccessCallback) {
                    onSuccessCallback();
                }
            },
            onError: (error: Error) => {
                toast.error(error.message || "Failed to use item");
            },
        })
    );
};

export default useEnhancedActivateItem;
