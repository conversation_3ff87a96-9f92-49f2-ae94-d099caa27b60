import { renderHook, act } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { useLoadingState, useMultipleLoadingStates } from "../useLoadingState";

describe("useLoadingState", () => {
    beforeEach(() => {
        vi.clearAllMocks();
        vi.useFakeTimers();
    });

    afterEach(() => {
        vi.useRealTimers();
    });

    it("should initialize with default state", () => {
        const { result } = renderHook(() => useLoadingState());

        expect(result.current.isLoading).toBe(false);
        expect(result.current.loadingState).toEqual({
            isLoading: false,
            type: "spinner",
            message: "Loading...",
            overlay: false,
        });
    });

    it("should initialize with custom options", () => {
        const { result } = renderHook(() =>
            useLoadingState({
                initialLoading: true,
                type: "skeleton",
                message: "Custom loading...",
                overlay: true,
            })
        );

        expect(result.current.isLoading).toBe(true);
        expect(result.current.loadingState).toEqual({
            isLoading: true,
            type: "skeleton",
            message: "Custom loading...",
            overlay: true,
        });
    });

    it("should start loading with default options", () => {
        const { result } = renderHook(() => useLoadingState());

        act(() => {
            result.current.startLoading();
        });

        expect(result.current.isLoading).toBe(true);
        expect(result.current.loadingState.type).toBe("spinner");
        expect(result.current.loadingState.message).toBe("Loading...");
    });

    it("should start loading with custom options", () => {
        const { result } = renderHook(() => useLoadingState());

        act(() => {
            result.current.startLoading({
                type: "overlay",
                message: "Processing...",
                progress: 50,
            });
        });

        expect(result.current.isLoading).toBe(true);
        expect(result.current.loadingState.type).toBe("overlay");
        expect(result.current.loadingState.message).toBe("Processing...");
        expect(result.current.loadingState.progress).toBe(50);
    });

    it("should stop loading", () => {
        const { result } = renderHook(() => useLoadingState());

        act(() => {
            result.current.startLoading();
        });

        expect(result.current.isLoading).toBe(true);

        act(() => {
            result.current.stopLoading();
        });

        expect(result.current.isLoading).toBe(false);
    });

    it("should respect minimum duration", () => {
        const { result } = renderHook(() =>
            useLoadingState({ minDuration: 1000 })
        );

        act(() => {
            result.current.startLoading();
        });

        expect(result.current.isLoading).toBe(true);

        // Try to stop immediately
        act(() => {
            result.current.stopLoading();
        });

        // Should still be loading due to minimum duration
        expect(result.current.isLoading).toBe(true);

        // Fast forward time
        act(() => {
            vi.advanceTimersByTime(1000);
        });

        expect(result.current.isLoading).toBe(false);
    });

    it("should update progress", () => {
        const { result } = renderHook(() => useLoadingState());

        act(() => {
            result.current.startLoading();
        });

        act(() => {
            result.current.updateProgress(75);
        });

        expect(result.current.loadingState.progress).toBe(75);
    });

    it("should clamp progress between 0 and 100", () => {
        const { result } = renderHook(() => useLoadingState());

        act(() => {
            result.current.startLoading();
        });

        act(() => {
            result.current.updateProgress(-10);
        });

        expect(result.current.loadingState.progress).toBe(0);

        act(() => {
            result.current.updateProgress(150);
        });

        expect(result.current.loadingState.progress).toBe(100);
    });

    it("should update message", () => {
        const { result } = renderHook(() => useLoadingState());

        act(() => {
            result.current.startLoading();
        });

        act(() => {
            result.current.updateMessage("New message");
        });

        expect(result.current.loadingState.message).toBe("New message");
    });

    it("should set loading type", () => {
        const { result } = renderHook(() => useLoadingState());

        act(() => {
            result.current.startLoading();
        });

        act(() => {
            result.current.setLoadingType("skeleton");
        });

        expect(result.current.loadingState.type).toBe("skeleton");
    });

    it("should handle timeout", () => {
        const onTimeout = vi.fn();
        const { result } = renderHook(() =>
            useLoadingState({
                maxDuration: 5000,
                onTimeout,
            })
        );

        act(() => {
            result.current.startLoading();
        });

        expect(result.current.isLoading).toBe(true);

        // Fast forward past timeout
        act(() => {
            vi.advanceTimersByTime(5000);
        });

        expect(onTimeout).toHaveBeenCalled();
        expect(result.current.isLoading).toBe(false);
    });
});

describe("useMultipleLoadingStates", () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    it("should initialize with empty states", () => {
        const { result } = renderHook(() => useMultipleLoadingStates());

        expect(result.current.loadingStates).toEqual({});
        expect(result.current.isAnyLoading).toBe(false);
    });

    it("should start loading for specific key", () => {
        const { result } = renderHook(() => useMultipleLoadingStates());

        act(() => {
            result.current.startLoading("test", {
                type: "spinner",
                message: "Test loading",
            });
        });

        expect(result.current.isLoading("test")).toBe(true);
        expect(result.current.isAnyLoading).toBe(true);
        expect(result.current.getLoadingState("test")).toEqual({
            isLoading: true,
            type: "spinner",
            message: "Test loading",
            overlay: false,
        });
    });

    it("should stop loading for specific key", () => {
        const { result } = renderHook(() => useMultipleLoadingStates());

        act(() => {
            result.current.startLoading("test");
        });

        expect(result.current.isLoading("test")).toBe(true);

        act(() => {
            result.current.stopLoading("test");
        });

        expect(result.current.isLoading("test")).toBe(false);
        expect(result.current.isAnyLoading).toBe(false);
    });

    it("should handle multiple concurrent loading states", () => {
        const { result } = renderHook(() => useMultipleLoadingStates());

        act(() => {
            result.current.startLoading("first");
            result.current.startLoading("second");
        });

        expect(result.current.isLoading("first")).toBe(true);
        expect(result.current.isLoading("second")).toBe(true);
        expect(result.current.isAnyLoading).toBe(true);

        act(() => {
            result.current.stopLoading("first");
        });

        expect(result.current.isLoading("first")).toBe(false);
        expect(result.current.isLoading("second")).toBe(true);
        expect(result.current.isAnyLoading).toBe(true);

        act(() => {
            result.current.stopLoading("second");
        });

        expect(result.current.isAnyLoading).toBe(false);
    });

    it("should update progress for specific key", () => {
        const { result } = renderHook(() => useMultipleLoadingStates());

        act(() => {
            result.current.startLoading("test");
        });

        act(() => {
            result.current.updateProgress("test", 50);
        });

        expect(result.current.getLoadingState("test")?.progress).toBe(50);
    });

    it("should update message for specific key", () => {
        const { result } = renderHook(() => useMultipleLoadingStates());

        act(() => {
            result.current.startLoading("test");
        });

        act(() => {
            result.current.updateMessage("test", "Updated message");
        });

        expect(result.current.getLoadingState("test")?.message).toBe("Updated message");
    });

    it("should return undefined for non-existent keys", () => {
        const { result } = renderHook(() => useMultipleLoadingStates());

        expect(result.current.getLoadingState("nonexistent")).toBeUndefined();
        expect(result.current.isLoading("nonexistent")).toBe(false);
    });
});
