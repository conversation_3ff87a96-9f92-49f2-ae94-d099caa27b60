import { renderHook, act } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { useErrorHandler } from "../useErrorHandler";
import { ApiError, UnauthorizedError, MaintenanceModeError } from "@/helpers/apiError";

// Mock dependencies
vi.mock("react-hot-toast", () => ({
    toast: {
        error: vi.fn(),
    },
}));

vi.mock("@/helpers/handleLogout", () => ({
    default: vi.fn(),
}));

describe("useErrorHandler", () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    it("should initialize with no error", () => {
        const { result } = renderHook(() => useErrorHandler());

        expect(result.current.error).toBeNull();
        expect(result.current.hasError).toBe(false);
        expect(result.current.retryCount).toBe(0);
        expect(result.current.isRetrying).toBe(false);
        expect(result.current.canRetry).toBe(false);
    });

    it("should handle API errors correctly", () => {
        const { result } = renderHook(() => useErrorHandler());
        const apiError = new ApiError("Test error", 500);

        act(() => {
            result.current.handleError(apiError);
        });

        expect(result.current.error).toEqual({
            message: "Test error",
            code: "ApiError",
            status: 500,
            type: "server",
            retryable: true,
            originalError: apiError,
        });
        expect(result.current.hasError).toBe(true);
    });

    it("should categorize network errors as retryable", () => {
        const { result } = renderHook(() => useErrorHandler());
        const networkError = new Error("Network connection failed");

        act(() => {
            result.current.handleError(networkError);
        });

        expect(result.current.error?.type).toBe("network");
        expect(result.current.error?.retryable).toBe(true);
    });

    it("should categorize auth errors as non-retryable", () => {
        const { result } = renderHook(() => useErrorHandler());
        const authError = new UnauthorizedError();

        act(() => {
            result.current.handleError(authError);
        });

        expect(result.current.error?.type).toBe("auth");
        expect(result.current.error?.retryable).toBe(false);
    });

    it("should handle maintenance mode errors", () => {
        const { result } = renderHook(() => useErrorHandler());
        const maintenanceError = new MaintenanceModeError();

        act(() => {
            result.current.handleError(maintenanceError);
        });

        expect(result.current.error?.type).toBe("maintenance");
        expect(result.current.error?.retryable).toBe(false);
    });

    it("should clear errors", () => {
        const { result } = renderHook(() => useErrorHandler());
        const error = new Error("Test error");

        act(() => {
            result.current.handleError(error);
        });

        expect(result.current.hasError).toBe(true);

        act(() => {
            result.current.clearError();
        });

        expect(result.current.error).toBeNull();
        expect(result.current.hasError).toBe(false);
        expect(result.current.retryCount).toBe(0);
    });

    it("should call custom error handler", () => {
        const onError = vi.fn();
        const { result } = renderHook(() => useErrorHandler({ onError }));
        const error = new Error("Test error");

        act(() => {
            result.current.handleError(error);
        });

        expect(onError).toHaveBeenCalledWith(
            expect.objectContaining({
                message: "Test error",
                type: "network",
            })
        );
    });

    it("should respect maxRetries setting", () => {
        const { result } = renderHook(() => useErrorHandler({ maxRetries: 2 }));
        const error = new Error("Network error");

        act(() => {
            result.current.handleError(error);
        });

        expect(result.current.canRetry).toBe(true);

        // Simulate retry attempts
        act(() => {
            result.current.retry();
        });
        act(() => {
            result.current.retry();
        });

        expect(result.current.retryCount).toBe(0); // Would be incremented in actual retry logic
        expect(result.current.canRetry).toBe(true); // Still can retry until maxRetries reached
    });

    it("should disable toast when showToast is false", () => {
        const { toast } = require("react-hot-toast");
        const { result } = renderHook(() => useErrorHandler({ showToast: false }));
        const error = new Error("Test error");

        act(() => {
            result.current.handleError(error);
        });

        expect(toast.error).not.toHaveBeenCalled();
    });

    it("should use fallback message for unknown errors", () => {
        const fallbackMessage = "Custom fallback message";
        const { result } = renderHook(() => useErrorHandler({ fallbackMessage }));
        const unknownError = "string error";

        act(() => {
            result.current.handleError(unknownError);
        });

        expect(result.current.error?.message).toBe(fallbackMessage);
        expect(result.current.error?.type).toBe("unknown");
    });

    it("should handle validation errors (4xx) as non-retryable", () => {
        const { result } = renderHook(() => useErrorHandler());
        const validationError = new ApiError("Validation failed", 400);

        act(() => {
            result.current.handleError(validationError);
        });

        expect(result.current.error?.type).toBe("validation");
        expect(result.current.error?.retryable).toBe(false);
    });

    it("should handle server errors (5xx) as retryable", () => {
        const { result } = renderHook(() => useErrorHandler());
        const serverError = new ApiError("Internal server error", 500);

        act(() => {
            result.current.handleError(serverError);
        });

        expect(result.current.error?.type).toBe("server");
        expect(result.current.error?.retryable).toBe(true);
    });
});
