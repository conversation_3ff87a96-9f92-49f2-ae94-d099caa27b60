import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Refresh<PERSON><PERSON>, Wifi, Wifi<PERSON>ff, Server, AlertTriangle, Bug, Home } from "lucide-react";
import { cn } from "@/lib/utils";
import type { AppError } from "@/hooks/useErrorHandler";

export interface ErrorDisplayProps {
    error: AppError;
    onRetry?: () => void;
    onDismiss?: () => void;
    onGoHome?: () => void;
    showDetails?: boolean;
    variant?: "inline" | "card" | "banner" | "modal";
    size?: "sm" | "md" | "lg";
    className?: string;
}

/**
 * Get appropriate icon for error type
 */
function getErrorIcon(type: AppError["type"]) {
    switch (type) {
        case "network":
            return WifiOff;
        case "server":
            return Server;
        case "auth":
            return AlertTriangle;
        case "maintenance":
            return AlertTriangle;
        case "validation":
            return AlertCircle;
        default:
            return Bug;
    }
}

/**
 * Get error severity color classes
 */
function getSeverityClasses(type: AppError["type"]) {
    switch (type) {
        case "auth":
        case "maintenance":
            return {
                bg: "bg-red-50 dark:bg-red-900/20",
                border: "border-red-200 dark:border-red-800",
                icon: "text-red-600 dark:text-red-400",
                text: "text-red-800 dark:text-red-200",
            };
        case "server":
        case "network":
            return {
                bg: "bg-orange-50 dark:bg-orange-900/20",
                border: "border-orange-200 dark:border-orange-800",
                icon: "text-orange-600 dark:text-orange-400",
                text: "text-orange-800 dark:text-orange-200",
            };
        case "validation":
            return {
                bg: "bg-yellow-50 dark:bg-yellow-900/20",
                border: "border-yellow-200 dark:border-yellow-800",
                icon: "text-yellow-600 dark:text-yellow-400",
                text: "text-yellow-800 dark:text-yellow-200",
            };
        default:
            return {
                bg: "bg-gray-50 dark:bg-gray-900/20",
                border: "border-gray-200 dark:border-gray-800",
                icon: "text-gray-600 dark:text-gray-400",
                text: "text-gray-800 dark:text-gray-200",
            };
    }
}

/**
 * Get user-friendly error title
 */
function getErrorTitle(error: AppError): string {
    switch (error.type) {
        case "network":
            return "Connection Problem";
        case "server":
            return "Server Error";
        case "auth":
            return "Authentication Required";
        case "maintenance":
            return "Maintenance Mode";
        case "validation":
            return "Invalid Input";
        default:
            return "Something Went Wrong";
    }
}

/**
 * Get user-friendly error message
 */
function getErrorMessage(error: AppError): string {
    switch (error.type) {
        case "network":
            return "Unable to connect to the server. Please check your internet connection and try again.";
        case "server":
            return "The server encountered an error. Please try again in a few moments.";
        case "auth":
            return "Your session has expired. Please log in again to continue.";
        case "maintenance":
            return "The system is currently undergoing maintenance. Please try again later.";
        case "validation":
            return error.message || "Please check your input and try again.";
        default:
            return error.message || "An unexpected error occurred. Please try again.";
    }
}

/**
 * Main error display component with multiple variants
 */
export function ErrorDisplay({
    error,
    onRetry,
    onDismiss,
    onGoHome,
    showDetails = false,
    variant = "card",
    size = "md",
    className,
}: ErrorDisplayProps) {
    const Icon = getErrorIcon(error.type);
    const colors = getSeverityClasses(error.type);
    const title = getErrorTitle(error);
    const message = getErrorMessage(error);
    
    const sizeClasses = {
        sm: {
            container: "p-3",
            icon: "w-5 h-5",
            title: "text-sm font-medium",
            message: "text-xs",
            button: "px-2 py-1 text-xs",
        },
        md: {
            container: "p-4",
            icon: "w-6 h-6",
            title: "text-base font-semibold",
            message: "text-sm",
            button: "px-3 py-2 text-sm",
        },
        lg: {
            container: "p-6",
            icon: "w-8 h-8",
            title: "text-lg font-semibold",
            message: "text-base",
            button: "px-4 py-2 text-base",
        },
    };
    
    const variantClasses = {
        inline: "flex items-start space-x-3",
        card: `rounded-lg border ${colors.border} ${colors.bg}`,
        banner: `border-l-4 ${colors.border} ${colors.bg}`,
        modal: `rounded-lg border ${colors.border} ${colors.bg} shadow-lg`,
    };
    
    const containerClasses = cn(
        variantClasses[variant],
        sizeClasses[size].container,
        className
    );
    
    return (
        <div className={containerClasses}>
            <div className="flex items-start space-x-3">
                <Icon className={cn(sizeClasses[size].icon, colors.icon, "flex-shrink-0 mt-0.5")} />
                
                <div className="flex-1 min-w-0">
                    <h3 className={cn(sizeClasses[size].title, colors.text)}>
                        {title}
                    </h3>
                    <p className={cn(sizeClasses[size].message, colors.text, "mt-1 opacity-90")}>
                        {message}
                    </p>
                    
                    {showDetails && error.code && (
                        <details className="mt-2">
                            <summary className={cn(
                                "cursor-pointer text-xs font-medium",
                                colors.text,
                                "opacity-75 hover:opacity-100"
                            )}>
                                Technical Details
                            </summary>
                            <div className="mt-1 text-xs opacity-75 space-y-1">
                                <div><strong>Error Code:</strong> {error.code}</div>
                                {error.status && <div><strong>Status:</strong> {error.status}</div>}
                                <div><strong>Message:</strong> {error.message}</div>
                            </div>
                        </details>
                    )}
                    
                    <div className="flex flex-wrap gap-2 mt-3">
                        {onRetry && error.retryable && (
                            <button
                                onClick={onRetry}
                                className={cn(
                                    sizeClasses[size].button,
                                    "inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors"
                                )}
                            >
                                <RefreshCw className="w-4 h-4 mr-1" />
                                Try Again
                            </button>
                        )}
                        
                        {onGoHome && error.type === "auth" && (
                            <button
                                onClick={onGoHome}
                                className={cn(
                                    sizeClasses[size].button,
                                    "inline-flex items-center bg-green-600 hover:bg-green-700 text-white rounded transition-colors"
                                )}
                            >
                                <Home className="w-4 h-4 mr-1" />
                                Go Home
                            </button>
                        )}
                        
                        {onDismiss && (
                            <button
                                onClick={onDismiss}
                                className={cn(
                                    sizeClasses[size].button,
                                    "inline-flex items-center bg-gray-600 hover:bg-gray-700 text-white rounded transition-colors"
                                )}
                            >
                                Dismiss
                            </button>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}

/**
 * Compact error display for inline use
 */
export function InlineErrorDisplay({
    error,
    onRetry,
    className,
}: Pick<ErrorDisplayProps, "error" | "onRetry" | "className">) {
    return (
        <ErrorDisplay
            error={error}
            onRetry={onRetry}
            variant="inline"
            size="sm"
            className={className}
        />
    );
}

/**
 * Banner error display for page-level errors
 */
export function BannerErrorDisplay({
    error,
    onRetry,
    onDismiss,
    className,
}: Pick<ErrorDisplayProps, "error" | "onRetry" | "onDismiss" | "className">) {
    return (
        <ErrorDisplay
            error={error}
            onRetry={onRetry}
            onDismiss={onDismiss}
            variant="banner"
            size="md"
            className={className}
        />
    );
}

/**
 * Card error display for component-level errors
 */
export function CardErrorDisplay({
    error,
    onRetry,
    showDetails = false,
    className,
}: Pick<ErrorDisplayProps, "error" | "onRetry" | "showDetails" | "className">) {
    return (
        <ErrorDisplay
            error={error}
            onRetry={onRetry}
            showDetails={showDetails}
            variant="card"
            size="md"
            className={className}
        />
    );
}

export default ErrorDisplay;
