import React, { useState } from "react";
import { useApi<PERSON>peration, useLoadingState, use<PERSON><PERSON>r<PERSON><PERSON><PERSON> } from "@/hooks";
import { LoadingSpinner, Skeleton, LoadingOverlay } from "@/components/Loading";
import { ErrorDisplay, CardErrorDisplay } from "@/components/Error";
import EnhancedErrorBoundary from "@/components/Layout/EnhancedErrorBoundary";

/**
 * Example component demonstrating the new unified error handling and loading state patterns
 * This serves as both documentation and a testing ground for the new patterns
 */
export function EnhancedErrorHandlingExample() {
    const [selectedDemo, setSelectedDemo] = useState<string>("api-operation");
    
    return (
        <EnhancedErrorBoundary
            level="component"
            enableRetry
            showDetails
            onError={(error, errorInfo) => {
                console.log("Error caught in example component:", { error, errorInfo });
            }}
        >
            <div className="max-w-4xl mx-auto p-6 space-y-8">
                <div className="text-center">
                    <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                        Enhanced Error Handling & Loading States
                    </h1>
                    <p className="text-gray-600 dark:text-gray-400">
                        Demonstration of unified error handling and loading state patterns
                    </p>
                </div>
                
                {/* Demo Selector */}
                <div className="flex flex-wrap gap-2 justify-center">
                    {[
                        { id: "api-operation", label: "API Operation" },
                        { id: "loading-states", label: "Loading States" },
                        { id: "error-displays", label: "Error Displays" },
                        { id: "skeleton-loaders", label: "Skeleton Loaders" },
                    ].map((demo) => (
                        <button
                            key={demo.id}
                            onClick={() => setSelectedDemo(demo.id)}
                            className={`px-4 py-2 rounded-lg transition-colors ${
                                selectedDemo === demo.id
                                    ? "bg-blue-600 text-white"
                                    : "bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600"
                            }`}
                        >
                            {demo.label}
                        </button>
                    ))}
                </div>
                
                {/* Demo Content */}
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                    {selectedDemo === "api-operation" && <ApiOperationDemo />}
                    {selectedDemo === "loading-states" && <LoadingStatesDemo />}
                    {selectedDemo === "error-displays" && <ErrorDisplaysDemo />}
                    {selectedDemo === "skeleton-loaders" && <SkeletonLoadersDemo />}
                </div>
            </div>
        </EnhancedErrorBoundary>
    );
}

/**
 * Demonstrates useApiOperation hook
 */
function ApiOperationDemo() {
    const apiOperation = useApiOperation(
        async (shouldFail: boolean) => {
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            if (shouldFail) {
                throw new Error("Simulated API error");
            }
            
            return { message: "API call successful!", timestamp: new Date().toISOString() };
        },
        {
            loadingMessage: "Calling API...",
            successMessage: "API call completed!",
        }
    );
    
    return (
        <div className="space-y-4">
            <h3 className="text-lg font-semibold">API Operation Demo</h3>
            
            <div className="flex gap-2">
                <button
                    onClick={() => apiOperation.execute(false)}
                    disabled={apiOperation.isLoading}
                    className="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded transition-colors"
                >
                    Success Call
                </button>
                <button
                    onClick={() => apiOperation.execute(true)}
                    disabled={apiOperation.isLoading}
                    className="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white rounded transition-colors"
                >
                    Error Call
                </button>
                <button
                    onClick={apiOperation.reset}
                    className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded transition-colors"
                >
                    Reset
                </button>
            </div>
            
            {apiOperation.isLoading && (
                <div className="flex items-center space-x-2">
                    <LoadingSpinner size="sm" />
                    <span>{apiOperation.loadingState.message}</span>
                </div>
            )}
            
            {apiOperation.hasError && (
                <CardErrorDisplay
                    error={apiOperation.error!}
                    onRetry={apiOperation.retry}
                    showDetails
                />
            )}
            
            {apiOperation.data && (
                <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded">
                    <h4 className="font-medium text-green-800 dark:text-green-200">Success!</h4>
                    <pre className="text-sm text-green-700 dark:text-green-300 mt-1">
                        {JSON.stringify(apiOperation.data, null, 2)}
                    </pre>
                </div>
            )}
        </div>
    );
}

/**
 * Demonstrates loading states
 */
function LoadingStatesDemo() {
    const loadingState = useLoadingState();
    const [overlayVisible, setOverlayVisible] = useState(false);
    
    return (
        <div className="space-y-4">
            <h3 className="text-lg font-semibold">Loading States Demo</h3>
            
            <div className="flex gap-2 flex-wrap">
                <button
                    onClick={() => {
                        loadingState.startLoading({ type: "spinner", message: "Loading data..." });
                        setTimeout(() => loadingState.stopLoading(), 3000);
                    }}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors"
                >
                    Spinner Loading
                </button>
                <button
                    onClick={() => {
                        setOverlayVisible(true);
                        setTimeout(() => setOverlayVisible(false), 3000);
                    }}
                    className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded transition-colors"
                >
                    Overlay Loading
                </button>
            </div>
            
            {loadingState.isLoading && (
                <div className="p-4 border rounded">
                    <LoadingSpinner variant={loadingState.loadingState.type as any} />
                    <p className="mt-2">{loadingState.loadingState.message}</p>
                </div>
            )}
            
            <LoadingOverlay
                isVisible={overlayVisible}
                message="Processing request..."
                variant="spinner"
                portal
            />
        </div>
    );
}

/**
 * Demonstrates error displays
 */
function ErrorDisplaysDemo() {
    const errorHandler = useErrorHandler();
    
    const simulateError = (type: string) => {
        const errors = {
            network: new Error("Network connection failed"),
            server: new Error("Internal server error"),
            validation: new Error("Invalid input provided"),
        };
        
        errorHandler.handleError(errors[type as keyof typeof errors] || new Error("Unknown error"));
    };
    
    return (
        <div className="space-y-4">
            <h3 className="text-lg font-semibold">Error Displays Demo</h3>
            
            <div className="flex gap-2 flex-wrap">
                <button
                    onClick={() => simulateError("network")}
                    className="px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded transition-colors"
                >
                    Network Error
                </button>
                <button
                    onClick={() => simulateError("server")}
                    className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded transition-colors"
                >
                    Server Error
                </button>
                <button
                    onClick={() => simulateError("validation")}
                    className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded transition-colors"
                >
                    Validation Error
                </button>
                <button
                    onClick={errorHandler.clearError}
                    className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded transition-colors"
                >
                    Clear Error
                </button>
            </div>
            
            {errorHandler.hasError && (
                <ErrorDisplay
                    error={errorHandler.error!}
                    onRetry={errorHandler.retry}
                    onDismiss={errorHandler.clearError}
                    showDetails
                    variant="card"
                />
            )}
        </div>
    );
}

/**
 * Demonstrates skeleton loaders
 */
function SkeletonLoadersDemo() {
    const [showSkeletons, setShowSkeletons] = useState(false);
    
    return (
        <div className="space-y-4">
            <h3 className="text-lg font-semibold">Skeleton Loaders Demo</h3>
            
            <button
                onClick={() => setShowSkeletons(!showSkeletons)}
                className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded transition-colors"
            >
                {showSkeletons ? "Hide" : "Show"} Skeletons
            </button>
            
            {showSkeletons ? (
                <div className="space-y-4">
                    <Skeleton height="2rem" width="60%" />
                    <Skeleton height="1rem" width="80%" />
                    <Skeleton height="1rem" width="70%" />
                    <div className="flex space-x-4">
                        <Skeleton variant="circular" width="3rem" height="3rem" />
                        <div className="flex-1 space-y-2">
                            <Skeleton height="1rem" width="40%" />
                            <Skeleton height="0.75rem" width="60%" />
                        </div>
                    </div>
                </div>
            ) : (
                <div className="space-y-4">
                    <h4 className="text-xl font-semibold">Sample Content</h4>
                    <p>This is some sample content that would be replaced by skeletons during loading.</p>
                    <div className="flex space-x-4">
                        <div className="w-12 h-12 bg-blue-500 rounded-full"></div>
                        <div>
                            <p className="font-medium">User Name</p>
                            <p className="text-sm text-gray-600"><EMAIL></p>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}

export default EnhancedErrorHandlingExample;
