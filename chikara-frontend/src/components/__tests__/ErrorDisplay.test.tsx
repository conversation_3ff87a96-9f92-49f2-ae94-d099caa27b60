import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import { ErrorDisplay, InlineErrorDisplay, BannerErrorDisplay, CardErrorDisplay } from "../Error/ErrorDisplay";
import type { AppError } from "@/hooks/useErrorHandler";

const mockNetworkError: AppError = {
    message: "Network connection failed",
    type: "network",
    retryable: true,
};

const mockValidationError: AppError = {
    message: "Invalid input provided",
    code: "VALIDATION_ERROR",
    status: 400,
    type: "validation",
    retryable: false,
};

const mockServerError: AppError = {
    message: "Internal server error",
    code: "SERVER_ERROR",
    status: 500,
    type: "server",
    retryable: true,
};

const mockAuthError: AppError = {
    message: "Authentication required",
    type: "auth",
    retryable: false,
};

describe("ErrorDisplay", () => {
    it("should render network error correctly", () => {
        render(<ErrorDisplay error={mockNetworkError} />);

        expect(screen.getByText("Connection Problem")).toBeInTheDocument();
        expect(screen.getByText(/Unable to connect to the server/)).toBeInTheDocument();
    });

    it("should render validation error correctly", () => {
        render(<ErrorDisplay error={mockValidationError} />);

        expect(screen.getByText("Invalid Input")).toBeInTheDocument();
        expect(screen.getByText("Invalid input provided")).toBeInTheDocument();
    });

    it("should render server error correctly", () => {
        render(<ErrorDisplay error={mockServerError} />);

        expect(screen.getByText("Server Error")).toBeInTheDocument();
        expect(screen.getByText(/The server encountered an error/)).toBeInTheDocument();
    });

    it("should render auth error correctly", () => {
        render(<ErrorDisplay error={mockAuthError} />);

        expect(screen.getByText("Authentication Required")).toBeInTheDocument();
        expect(screen.getByText(/Your session has expired/)).toBeInTheDocument();
    });

    it("should show retry button for retryable errors", () => {
        const onRetry = vi.fn();
        render(<ErrorDisplay error={mockNetworkError} onRetry={onRetry} />);

        const retryButton = screen.getByText("Try Again");
        expect(retryButton).toBeInTheDocument();

        fireEvent.click(retryButton);
        expect(onRetry).toHaveBeenCalledTimes(1);
    });

    it("should not show retry button for non-retryable errors", () => {
        const onRetry = vi.fn();
        render(<ErrorDisplay error={mockValidationError} onRetry={onRetry} />);

        expect(screen.queryByText("Try Again")).not.toBeInTheDocument();
    });

    it("should show dismiss button when provided", () => {
        const onDismiss = vi.fn();
        render(<ErrorDisplay error={mockNetworkError} onDismiss={onDismiss} />);

        const dismissButton = screen.getByText("Dismiss");
        expect(dismissButton).toBeInTheDocument();

        fireEvent.click(dismissButton);
        expect(onDismiss).toHaveBeenCalledTimes(1);
    });

    it("should show go home button for auth errors", () => {
        const onGoHome = vi.fn();
        render(<ErrorDisplay error={mockAuthError} onGoHome={onGoHome} />);

        const goHomeButton = screen.getByText("Go Home");
        expect(goHomeButton).toBeInTheDocument();

        fireEvent.click(goHomeButton);
        expect(onGoHome).toHaveBeenCalledTimes(1);
    });

    it("should show technical details when enabled", () => {
        render(<ErrorDisplay error={mockValidationError} showDetails />);

        const detailsToggle = screen.getByText("Technical Details");
        expect(detailsToggle).toBeInTheDocument();

        fireEvent.click(detailsToggle);

        expect(screen.getByText("VALIDATION_ERROR")).toBeInTheDocument();
        expect(screen.getByText("400")).toBeInTheDocument();
    });

    it("should apply correct variant classes", () => {
        const { container } = render(
            <ErrorDisplay error={mockNetworkError} variant="banner" />
        );

        const errorElement = container.firstChild as HTMLElement;
        expect(errorElement).toHaveClass("border-l-4");
    });

    it("should apply correct size classes", () => {
        render(<ErrorDisplay error={mockNetworkError} size="lg" />);

        // Check for large size icon (w-8 h-8)
        const icon = screen.getByRole("img", { hidden: true });
        expect(icon).toHaveClass("w-8", "h-8");
    });
});

describe("InlineErrorDisplay", () => {
    it("should render with inline variant", () => {
        const { container } = render(<InlineErrorDisplay error={mockNetworkError} />);

        const errorElement = container.firstChild as HTMLElement;
        expect(errorElement).toHaveClass("flex", "items-start", "space-x-3");
    });

    it("should use small size", () => {
        render(<InlineErrorDisplay error={mockNetworkError} />);

        const icon = screen.getByRole("img", { hidden: true });
        expect(icon).toHaveClass("w-5", "h-5");
    });
});

describe("BannerErrorDisplay", () => {
    it("should render with banner variant", () => {
        const { container } = render(<BannerErrorDisplay error={mockNetworkError} />);

        const errorElement = container.firstChild as HTMLElement;
        expect(errorElement).toHaveClass("border-l-4");
    });

    it("should show dismiss button", () => {
        const onDismiss = vi.fn();
        render(<BannerErrorDisplay error={mockNetworkError} onDismiss={onDismiss} />);

        expect(screen.getByText("Dismiss")).toBeInTheDocument();
    });
});

describe("CardErrorDisplay", () => {
    it("should render with card variant", () => {
        const { container } = render(<CardErrorDisplay error={mockNetworkError} />);

        const errorElement = container.firstChild as HTMLElement;
        expect(errorElement).toHaveClass("rounded-lg", "border");
    });

    it("should show details when enabled", () => {
        render(<CardErrorDisplay error={mockValidationError} showDetails />);

        expect(screen.getByText("Technical Details")).toBeInTheDocument();
    });
});

describe("Error severity styling", () => {
    it("should apply red styling for auth errors", () => {
        render(<ErrorDisplay error={mockAuthError} />);

        const icon = screen.getByRole("img", { hidden: true });
        expect(icon).toHaveClass("text-red-600");
    });

    it("should apply orange styling for network errors", () => {
        render(<ErrorDisplay error={mockNetworkError} />);

        const icon = screen.getByRole("img", { hidden: true });
        expect(icon).toHaveClass("text-orange-600");
    });

    it("should apply yellow styling for validation errors", () => {
        render(<ErrorDisplay error={mockValidationError} />);

        const icon = screen.getByRole("img", { hidden: true });
        expect(icon).toHaveClass("text-yellow-600");
    });
});
